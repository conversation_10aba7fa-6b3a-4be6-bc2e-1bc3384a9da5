﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using WzComparerR2.CharaSim;

namespace WzComparerR2.CharaSimControl
{
    public class ObjectMouseEventArgs : MouseEventArgs
    {
        public ObjectMouseEventArgs(Mouse<PERSON>ventArgs e, object obj)
            : this(e.<PERSON><PERSON>, e<PERSON>lick<PERSON>, e.X, e.Y, e.Delta, obj)
        {
        }

        public ObjectMouseEventArgs(MouseButt<PERSON> button, int clicks, int x, int y, int delta, object obj) :
            base(button, clicks, x, y, delta)
        {
            this.obj = obj;
        }

        private object obj;

        public object Obj
        {
            get { return obj; }
            set { obj = value; }
        }
    }
}
