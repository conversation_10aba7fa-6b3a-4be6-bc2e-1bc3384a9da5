﻿namespace MapleNecrocer
{
    partial class ViewForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            Tile = new CheckBox();
            Obj = new CheckBox();
            Back = new CheckBox();
            Front = new CheckBox();
            Npc = new CheckBox();
            Mob = new CheckBox();
            Portal = new CheckBox();
            ID = new CheckBox();
            BgmName = new CheckBox();
            UI = new CheckBox();
            MiniMap = new CheckBox();
            PortalInfo = new CheckBox();
            MobInfo = new CheckBox();
            MobName = new CheckBox();
            Foothold = new CheckBox();
            Player = new CheckBox();
            NpcName = new CheckBox();
            NpcChat = new CheckBox();
            panel1 = new Panel();
            button1 = new Button();
            textBox1 = new TextBox();
            label1 = new Label();
            ScrollingBar = new CheckBox();
            textBox2 = new TextBox();
            panel1.SuspendLayout();
            SuspendLayout();
            // 
            // Tile
            // 
            Tile.AutoSize = true;
            Tile.Checked = true;
            Tile.CheckState = CheckState.Checked;
            Tile.Location = new Point(12, 12);
            Tile.Name = "Tile";
            Tile.Size = new Size(52, 22);
            Tile.TabIndex = 0;
            Tile.Text = "Tile";
            Tile.UseVisualStyleBackColor = true;
            Tile.CheckedChanged += Tile_CheckedChanged;
            // 
            // Obj
            // 
            Obj.AutoSize = true;
            Obj.Checked = true;
            Obj.CheckState = CheckState.Checked;
            Obj.Location = new Point(70, 12);
            Obj.Name = "Obj";
            Obj.Size = new Size(53, 22);
            Obj.TabIndex = 1;
            Obj.Text = "Obj";
            Obj.UseVisualStyleBackColor = true;
            Obj.CheckedChanged += Tile_CheckedChanged;
            // 
            // Back
            // 
            Back.AutoSize = true;
            Back.Checked = true;
            Back.CheckState = CheckState.Checked;
            Back.Location = new Point(129, 12);
            Back.Name = "Back";
            Back.Size = new Size(61, 22);
            Back.TabIndex = 2;
            Back.Text = "Back";
            Back.UseVisualStyleBackColor = true;
            Back.CheckedChanged += Tile_CheckedChanged;
            // 
            // Front
            // 
            Front.AutoSize = true;
            Front.Checked = true;
            Front.CheckState = CheckState.Checked;
            Front.Location = new Point(196, 12);
            Front.Name = "Front";
            Front.Size = new Size(64, 22);
            Front.TabIndex = 3;
            Front.Text = "Front";
            Front.UseVisualStyleBackColor = true;
            Front.CheckedChanged += Tile_CheckedChanged;
            // 
            // Npc
            // 
            Npc.AutoSize = true;
            Npc.Checked = true;
            Npc.CheckState = CheckState.Checked;
            Npc.Location = new Point(266, 12);
            Npc.Name = "Npc";
            Npc.Size = new Size(55, 22);
            Npc.TabIndex = 4;
            Npc.Text = "Npc";
            Npc.UseVisualStyleBackColor = true;
            Npc.CheckedChanged += Tile_CheckedChanged;
            // 
            // Mob
            // 
            Mob.AutoSize = true;
            Mob.Checked = true;
            Mob.CheckState = CheckState.Checked;
            Mob.Location = new Point(327, 12);
            Mob.Name = "Mob";
            Mob.Size = new Size(58, 22);
            Mob.TabIndex = 5;
            Mob.Text = "Mob";
            Mob.UseVisualStyleBackColor = true;
            Mob.CheckedChanged += Tile_CheckedChanged;
            // 
            // Portal
            // 
            Portal.AutoSize = true;
            Portal.Checked = true;
            Portal.CheckState = CheckState.Checked;
            Portal.Location = new Point(391, 12);
            Portal.Name = "Portal";
            Portal.Size = new Size(66, 22);
            Portal.TabIndex = 6;
            Portal.Text = "Portal";
            Portal.UseVisualStyleBackColor = true;
            Portal.CheckedChanged += Tile_CheckedChanged;
            // 
            // ID
            // 
            ID.AutoSize = true;
            ID.Location = new Point(463, 12);
            ID.Name = "ID";
            ID.Size = new Size(46, 22);
            ID.TabIndex = 7;
            ID.Text = "ID";
            ID.UseVisualStyleBackColor = true;
            ID.CheckedChanged += Tile_CheckedChanged;
            // 
            // BgmName
            // 
            BgmName.AutoSize = true;
            BgmName.Location = new Point(515, 12);
            BgmName.Name = "BgmName";
            BgmName.Size = new Size(104, 22);
            BgmName.TabIndex = 8;
            BgmName.Text = "Bgm Name";
            BgmName.UseVisualStyleBackColor = true;
            BgmName.CheckedChanged += Tile_CheckedChanged;
            // 
            // UI
            // 
            UI.AutoSize = true;
            UI.Enabled = false;
            UI.Location = new Point(627, 11);
            UI.Name = "UI";
            UI.Size = new Size(46, 22);
            UI.TabIndex = 9;
            UI.Text = "UI";
            UI.UseVisualStyleBackColor = true;
            // 
            // MiniMap
            // 
            MiniMap.AutoSize = true;
            MiniMap.Checked = true;
            MiniMap.CheckState = CheckState.Checked;
            MiniMap.Location = new Point(12, 54);
            MiniMap.Name = "MiniMap";
            MiniMap.Size = new Size(87, 22);
            MiniMap.TabIndex = 10;
            MiniMap.Text = "Mini Map";
            MiniMap.UseVisualStyleBackColor = true;
            MiniMap.CheckedChanged += Tile_CheckedChanged;
            // 
            // PortalInfo
            // 
            PortalInfo.AutoSize = true;
            PortalInfo.Location = new Point(105, 54);
            PortalInfo.Name = "PortalInfo";
            PortalInfo.Size = new Size(98, 22);
            PortalInfo.TabIndex = 11;
            PortalInfo.Text = "Portal Info";
            PortalInfo.UseVisualStyleBackColor = true;
            PortalInfo.CheckedChanged += Tile_CheckedChanged;
            // 
            // MobInfo
            // 
            MobInfo.AutoSize = true;
            MobInfo.Enabled = false;
            MobInfo.Location = new Point(209, 54);
            MobInfo.Name = "MobInfo";
            MobInfo.Size = new Size(90, 22);
            MobInfo.TabIndex = 12;
            MobInfo.Text = "Mob Info";
            MobInfo.UseVisualStyleBackColor = true;
            // 
            // MobName
            // 
            MobName.AutoSize = true;
            MobName.Location = new Point(305, 54);
            MobName.Name = "MobName";
            MobName.Size = new Size(123, 22);
            MobName.TabIndex = 13;
            MobName.Text = "Mob Lv/Name";
            MobName.UseVisualStyleBackColor = true;
            MobName.CheckedChanged += Tile_CheckedChanged;
            // 
            // Foothold
            // 
            Foothold.AutoSize = true;
            Foothold.Location = new Point(424, 54);
            Foothold.Name = "Foothold";
            Foothold.Size = new Size(85, 22);
            Foothold.TabIndex = 14;
            Foothold.Text = "Foothold";
            Foothold.UseVisualStyleBackColor = true;
            Foothold.CheckedChanged += Tile_CheckedChanged;
            // 
            // Player
            // 
            Player.AutoSize = true;
            Player.Checked = true;
            Player.CheckState = CheckState.Checked;
            Player.Location = new Point(515, 54);
            Player.Name = "Player";
            Player.Size = new Size(69, 22);
            Player.TabIndex = 15;
            Player.Text = "Player";
            Player.UseVisualStyleBackColor = true;
            Player.CheckedChanged += Tile_CheckedChanged;
            // 
            // NpcName
            // 
            NpcName.AutoSize = true;
            NpcName.Checked = true;
            NpcName.CheckState = CheckState.Checked;
            NpcName.Location = new Point(590, 54);
            NpcName.Name = "NpcName";
            NpcName.Size = new Size(99, 22);
            NpcName.TabIndex = 16;
            NpcName.Text = "Npc Name";
            NpcName.UseVisualStyleBackColor = true;
            NpcName.CheckedChanged += Tile_CheckedChanged;
            // 
            // NpcChat
            // 
            NpcChat.AutoSize = true;
            NpcChat.Checked = true;
            NpcChat.CheckState = CheckState.Checked;
            NpcChat.Location = new Point(590, 91);
            NpcChat.Name = "NpcChat";
            NpcChat.Size = new Size(90, 22);
            NpcChat.TabIndex = 17;
            NpcChat.Text = "Npc Chat";
            NpcChat.UseVisualStyleBackColor = true;
            NpcChat.CheckedChanged += Tile_CheckedChanged;
            // 
            // panel1
            // 
            panel1.BorderStyle = BorderStyle.FixedSingle;
            panel1.Controls.Add(button1);
            panel1.Controls.Add(textBox1);
            panel1.Controls.Add(label1);
            panel1.Location = new Point(12, 91);
            panel1.Name = "panel1";
            panel1.Size = new Size(416, 35);
            panel1.TabIndex = 18;
            // 
            // button1
            // 
            button1.ImageAlign = ContentAlignment.TopCenter;
            button1.Location = new Point(341, 4);
            button1.Name = "button1";
            button1.Size = new Size(70, 25);
            button1.TabIndex = 2;
            button1.Text = "OK";
            button1.TextAlign = ContentAlignment.TopCenter;
            button1.UseVisualStyleBackColor = true;
            button1.Click += button1_Click;
            // 
            // textBox1
            // 
            textBox1.Font = new Font("Segoe UI", 15F, FontStyle.Regular, GraphicsUnit.Pixel);
            textBox1.Location = new Point(81, 3);
            textBox1.Name = "textBox1";
            textBox1.Size = new Size(256, 27);
            textBox1.TabIndex = 1;
            textBox1.Text = "SuperGM";
            textBox1.TextAlign = HorizontalAlignment.Right;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(7, 8);
            label1.Name = "label1";
            label1.Size = new Size(68, 18);
            label1.TabIndex = 0;
            label1.Text = "Player ID";
            // 
            // ScrollingBar
            // 
            ScrollingBar.AutoSize = true;
            ScrollingBar.Location = new Point(15, 141);
            ScrollingBar.Name = "ScrollingBar";
            ScrollingBar.Size = new Size(107, 22);
            ScrollingBar.TabIndex = 19;
            ScrollingBar.Text = "Scrolling Bar";
            ScrollingBar.UseVisualStyleBackColor = true;
            // 
            // textBox2
            // 
            textBox2.Font = new Font("Segoe UI", 15F, FontStyle.Regular, GraphicsUnit.Pixel);
            textBox2.Location = new Point(117, 139);
            textBox2.Name = "textBox2";
            textBox2.Size = new Size(455, 27);
            textBox2.TabIndex = 20;
            // 
            // ViewForm
            // 
            AutoScaleMode = AutoScaleMode.None;
            ClientSize = new Size(682, 173);
            Controls.Add(textBox2);
            Controls.Add(ScrollingBar);
            Controls.Add(panel1);
            Controls.Add(NpcChat);
            Controls.Add(NpcName);
            Controls.Add(Player);
            Controls.Add(Foothold);
            Controls.Add(MobName);
            Controls.Add(MobInfo);
            Controls.Add(PortalInfo);
            Controls.Add(MiniMap);
            Controls.Add(UI);
            Controls.Add(BgmName);
            Controls.Add(ID);
            Controls.Add(Portal);
            Controls.Add(Mob);
            Controls.Add(Npc);
            Controls.Add(Front);
            Controls.Add(Back);
            Controls.Add(Obj);
            Controls.Add(Tile);
            Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            KeyPreview = true;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "ViewForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "View";
            TopMost = true;
            Shown += ViewForm_Shown;
            KeyDown += ViewForm_KeyDown;
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private CheckBox Tile;
        private CheckBox Obj;
        private CheckBox Back;
        private CheckBox Front;
        private CheckBox Npc;
        private CheckBox Mob;
        private CheckBox Portal;
        private CheckBox ID;
        private CheckBox BgmName;
        private CheckBox UI;
        private CheckBox MiniMap;
        private CheckBox PortalInfo;
        private CheckBox MobInfo;
        private CheckBox MobName;
        private CheckBox Foothold;
        private CheckBox Player;
        private CheckBox NpcName;
        private CheckBox NpcChat;
        private Panel panel1;
        private Button button1;
        private TextBox textBox1;
        private Label label1;
        private CheckBox ScrollingBar;
        private TextBox textBox2;
    }
}