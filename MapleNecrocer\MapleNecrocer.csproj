﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Platforms>AnyCPU;x64</Platforms>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <ApplicationIcon>MapleStoryGM_Icon.ico</ApplicationIcon>
   
  </PropertyGroup>

  
  <ItemGroup>
    <Content Include="MapleStoryGM_Icon.ico" />
  </ItemGroup>

  
  <ItemGroup>
    <ProjectReference Include="..\WzComparerR2.Common\WzComparerR2.Common.csproj" />
    <ProjectReference Include="..\WzComparerR2.PluginBase\WzComparerR2.PluginBase.csproj" />
    <ProjectReference Include="..\WzComparerR2.WzLib\WzComparerR2.WzLib.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="AForge">
      <HintPath>..\Reference\AForge.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Imaging">
      <HintPath>..\Reference\AForge.Imaging.dll</HintPath>
    </Reference>
    <Reference Include="CharaSimResource">
      <HintPath>..\Reference\CharaSimResource.dll</HintPath>
    </Reference>
    <Reference Include="DevComponents.DotNetBar2">
      <HintPath>..\Reference\DevComponents.DotNetBar2.dll</HintPath>
    </Reference>
    <Reference Include="ImageListView">
      <HintPath>..\Reference\ImageListView.dll</HintPath>
    </Reference>
    <Reference Include="ManagedBass">
      <HintPath>..\Reference\ManagedBass.dll</HintPath>
    </Reference>
    <Reference Include="MonoGame.Forms.DX">
      <HintPath>..\Reference\MonoGame.Forms.DX.dll</HintPath>
    </Reference>
    <Reference Include="MonoGame.Framework">
      <HintPath>..\Reference\MonoGame.Framework.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX">
      <HintPath>..\Reference\SharpDX.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.Direct2D1">
      <HintPath>..\Reference\SharpDX.Direct2D1.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.Direct3D11">
      <HintPath>..\Reference\SharpDX.Direct3D11.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.DXGI">
      <HintPath>..\Reference\SharpDX.DXGI.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.Mathematics">
      <HintPath>..\Reference\SharpDX.Mathematics.dll</HintPath>
    </Reference>
    <Reference Include="spine-monogame">
      <HintPath>..\Reference\spine-monogame.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="copy &quot;$(ProjectDir)..\Reference\bass.dll&quot; &quot;$(TargetDir)bass.dll&quot;" />
  </Target>

</Project>