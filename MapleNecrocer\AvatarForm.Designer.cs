﻿namespace MapleNecrocer
{
    partial class AvatarForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(AvatarForm));
            button1 = new Button();
            button2 = new Button();
            button3 = new Button();
            button4 = new Button();
            button5 = new Button();
            button6 = new Button();
            button7 = new Button();
            button8 = new Button();
            button9 = new Button();
            button10 = new Button();
            button11 = new Button();
            button12 = new Button();
            button13 = new Button();
            button14 = new Button();
            button15 = new Button();
            button16 = new Button();
            button17 = new Button();
            button18 = new Button();
            button19 = new Button();
            button20 = new Button();
            SaveCharButton = new Button();
            tabControl1 = new TabControl();
            tabPage1 = new TabPage();
            tabPage2 = new TabPage();
            label9 = new Label();
            tabPage3 = new TabPage();
            DyeGrid = new DataGridView();
            tabPage7 = new TabPage();
            button22 = new Button();
            LabelLightness = new Label();
            LabelSat = new Label();
            LabelHue = new Label();
            label8 = new Label();
            label7 = new Label();
            label6 = new Label();
            LightnessTrackBar = new TrackBar();
            SatTrackBar = new TrackBar();
            HueTrackBar = new TrackBar();
            DyePicture = new PictureBox();
            DyeGrid2 = new DataGridView();
            tabPage4 = new TabPage();
            textBox1 = new TextBox();
            label3 = new Label();
            panel1 = new Panel();
            UseButton = new Button();
            pictureBox1 = new PictureBox();
            label1 = new Label();
            label2 = new Label();
            tabPage5 = new TabPage();
            tabPage6 = new TabPage();
            checkBox1 = new CheckBox();
            saveSprite_button = new Button();
            saveSpriteSheet_button = new Button();
            saveAllSprite_button = new Button();
            customAABB_checkBox = new CheckBox();
            groupBox1 = new GroupBox();
            ScrollBarH = new HScrollBar();
            label10 = new Label();
            AdjH = new Label();
            Xlabel = new Label();
            AdjX = new Label();
            ScrollBarX = new HScrollBar();
            ScrollBarW = new HScrollBar();
            AdjW = new Label();
            YLabel = new Label();
            AdjY = new Label();
            label13 = new Label();
            ScrollBarY = new HScrollBar();
            panel2 = new Panel();
            FrameListBox = new ListBox();
            label4 = new Label();
            comboBox1 = new ComboBox();
            ShowToolTil_CheckBox = new CheckBox();
            label5 = new Label();
            EarListBox = new ComboBox();
            timer1 = new System.Windows.Forms.Timer(components);
            tabControl1.SuspendLayout();
            tabPage2.SuspendLayout();
            tabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)DyeGrid).BeginInit();
            tabPage7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)LightnessTrackBar).BeginInit();
            ((System.ComponentModel.ISupportInitialize)SatTrackBar).BeginInit();
            ((System.ComponentModel.ISupportInitialize)HueTrackBar).BeginInit();
            ((System.ComponentModel.ISupportInitialize)DyePicture).BeginInit();
            ((System.ComponentModel.ISupportInitialize)DyeGrid2).BeginInit();
            tabPage4.SuspendLayout();
            panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            tabPage6.SuspendLayout();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // button1
            // 
            button1.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button1.Image = (Image)resources.GetObject("button1.Image");
            button1.ImageAlign = ContentAlignment.MiddleRight;
            button1.Location = new Point(12, 12);
            button1.Name = "button1";
            button1.Size = new Size(103, 41);
            button1.TabIndex = 0;
            button1.Tag = "20";
            button1.Text = "  Head";
            button1.TextAlign = ContentAlignment.MiddleLeft;
            button1.UseVisualStyleBackColor = true;
            button1.Click += button1_Click;
            // 
            // button2
            // 
            button2.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button2.Image = (Image)resources.GetObject("button2.Image");
            button2.ImageAlign = ContentAlignment.MiddleRight;
            button2.Location = new Point(121, 12);
            button2.Name = "button2";
            button2.Size = new Size(103, 41);
            button2.TabIndex = 1;
            button2.Tag = "1";
            button2.Text = "   Body";
            button2.TextAlign = ContentAlignment.MiddleLeft;
            button2.UseVisualStyleBackColor = true;
            button2.Click += button1_Click;
            // 
            // button3
            // 
            button3.Font = new Font("Tahoma", 8F);
            button3.Image = (Image)resources.GetObject("button3.Image");
            button3.ImageAlign = ContentAlignment.MiddleRight;
            button3.Location = new Point(448, 106);
            button3.Name = "button3";
            button3.Size = new Size(103, 41);
            button3.TabIndex = 2;
            button3.Tag = "2";
            button3.Text = "Weapon-1";
            button3.TextAlign = ContentAlignment.MiddleLeft;
            button3.UseVisualStyleBackColor = true;
            button3.Click += button1_Click;
            // 
            // button4
            // 
            button4.Font = new Font("Tahoma", 8F);
            button4.Image = (Image)resources.GetObject("button4.Image");
            button4.ImageAlign = ContentAlignment.MiddleRight;
            button4.Location = new Point(557, 106);
            button4.Name = "button4";
            button4.Size = new Size(103, 41);
            button4.TabIndex = 3;
            button4.Tag = "3";
            button4.Text = "Weapon-2";
            button4.TextAlign = ContentAlignment.MiddleLeft;
            button4.UseVisualStyleBackColor = true;
            button4.Click += button1_Click;
            // 
            // button5
            // 
            button5.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button5.Image = (Image)resources.GetObject("button5.Image");
            button5.ImageAlign = ContentAlignment.MiddleRight;
            button5.Location = new Point(12, 59);
            button5.Name = "button5";
            button5.Size = new Size(103, 41);
            button5.TabIndex = 4;
            button5.Tag = "4";
            button5.Text = "  Cap-1";
            button5.TextAlign = ContentAlignment.MiddleLeft;
            button5.UseVisualStyleBackColor = true;
            button5.Click += button1_Click;
            // 
            // button6
            // 
            button6.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button6.Image = (Image)resources.GetObject("button6.Image");
            button6.ImageAlign = ContentAlignment.MiddleRight;
            button6.Location = new Point(121, 59);
            button6.Name = "button6";
            button6.Size = new Size(103, 41);
            button6.TabIndex = 5;
            button6.Tag = "5";
            button6.Text = "  Cap-2";
            button6.TextAlign = ContentAlignment.MiddleLeft;
            button6.UseVisualStyleBackColor = true;
            button6.Click += button1_Click;
            // 
            // button7
            // 
            button7.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button7.Image = (Image)resources.GetObject("button7.Image");
            button7.ImageAlign = ContentAlignment.MiddleRight;
            button7.Location = new Point(230, 59);
            button7.Name = "button7";
            button7.Size = new Size(103, 41);
            button7.TabIndex = 6;
            button7.Tag = "6";
            button7.Text = "  Coat";
            button7.TextAlign = ContentAlignment.MiddleLeft;
            button7.UseVisualStyleBackColor = true;
            button7.Click += button1_Click;
            // 
            // button8
            // 
            button8.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button8.Image = (Image)resources.GetObject("button8.Image");
            button8.ImageAlign = ContentAlignment.MiddleRight;
            button8.Location = new Point(339, 59);
            button8.Name = "button8";
            button8.Size = new Size(103, 41);
            button8.TabIndex = 7;
            button8.Tag = "7";
            button8.Text = "  Pants";
            button8.TextAlign = ContentAlignment.MiddleLeft;
            button8.UseVisualStyleBackColor = true;
            button8.Click += button1_Click;
            // 
            // button9
            // 
            button9.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button9.Image = (Image)resources.GetObject("button9.Image");
            button9.ImageAlign = ContentAlignment.MiddleRight;
            button9.Location = new Point(448, 59);
            button9.Name = "button9";
            button9.Size = new Size(103, 41);
            button9.TabIndex = 8;
            button9.Tag = "8";
            button9.Text = "Longcoat";
            button9.TextAlign = ContentAlignment.MiddleLeft;
            button9.UseVisualStyleBackColor = true;
            button9.Click += button1_Click;
            // 
            // button10
            // 
            button10.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button10.Image = (Image)resources.GetObject("button10.Image");
            button10.ImageAlign = ContentAlignment.MiddleRight;
            button10.Location = new Point(557, 59);
            button10.Name = "button10";
            button10.Size = new Size(103, 41);
            button10.TabIndex = 9;
            button10.Tag = "9";
            button10.Text = "  Cape";
            button10.TextAlign = ContentAlignment.MiddleLeft;
            button10.UseVisualStyleBackColor = true;
            button10.Click += button1_Click;
            // 
            // button11
            // 
            button11.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button11.Image = (Image)resources.GetObject("button11.Image");
            button11.ImageAlign = ContentAlignment.MiddleRight;
            button11.Location = new Point(121, 153);
            button11.Name = "button11";
            button11.Size = new Size(103, 41);
            button11.TabIndex = 10;
            button11.Tag = "10";
            button11.Text = "  Shield";
            button11.TextAlign = ContentAlignment.MiddleLeft;
            button11.UseVisualStyleBackColor = true;
            button11.Click += button1_Click;
            // 
            // button12
            // 
            button12.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button12.Image = (Image)resources.GetObject("button12.Image");
            button12.ImageAlign = ContentAlignment.MiddleRight;
            button12.Location = new Point(121, 106);
            button12.Name = "button12";
            button12.Size = new Size(103, 41);
            button12.TabIndex = 11;
            button12.Tag = "11";
            button12.Text = "  Glove";
            button12.TextAlign = ContentAlignment.MiddleLeft;
            button12.UseVisualStyleBackColor = true;
            button12.Click += button1_Click;
            // 
            // button13
            // 
            button13.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button13.Image = (Image)resources.GetObject("button13.Image");
            button13.ImageAlign = ContentAlignment.MiddleRight;
            button13.Location = new Point(12, 106);
            button13.Name = "button13";
            button13.Size = new Size(103, 41);
            button13.TabIndex = 12;
            button13.Tag = "12";
            button13.Text = "  Shoes";
            button13.TextAlign = ContentAlignment.MiddleLeft;
            button13.UseVisualStyleBackColor = true;
            button13.Click += button1_Click;
            // 
            // button14
            // 
            button14.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button14.Image = (Image)resources.GetObject("button14.Image");
            button14.ImageAlign = ContentAlignment.MiddleRight;
            button14.Location = new Point(230, 12);
            button14.Name = "button14";
            button14.Size = new Size(103, 41);
            button14.TabIndex = 13;
            button14.Tag = "13";
            button14.Text = "Hair-1";
            button14.TextAlign = ContentAlignment.MiddleLeft;
            button14.UseVisualStyleBackColor = true;
            button14.Click += button1_Click;
            // 
            // button15
            // 
            button15.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button15.Image = (Image)resources.GetObject("button15.Image");
            button15.ImageAlign = ContentAlignment.MiddleRight;
            button15.Location = new Point(339, 12);
            button15.Name = "button15";
            button15.Size = new Size(103, 41);
            button15.TabIndex = 14;
            button15.Tag = "14";
            button15.Text = "Hair-2";
            button15.TextAlign = ContentAlignment.MiddleLeft;
            button15.UseVisualStyleBackColor = true;
            button15.Click += button1_Click;
            // 
            // button16
            // 
            button16.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button16.Image = (Image)resources.GetObject("button16.Image");
            button16.ImageAlign = ContentAlignment.MiddleRight;
            button16.Location = new Point(448, 12);
            button16.Name = "button16";
            button16.Size = new Size(103, 41);
            button16.TabIndex = 15;
            button16.Tag = "15";
            button16.Text = "  Face-1";
            button16.TextAlign = ContentAlignment.MiddleLeft;
            button16.UseVisualStyleBackColor = true;
            button16.Click += button1_Click;
            // 
            // button17
            // 
            button17.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button17.Image = (Image)resources.GetObject("button17.Image");
            button17.ImageAlign = ContentAlignment.MiddleRight;
            button17.Location = new Point(557, 12);
            button17.Name = "button17";
            button17.Size = new Size(103, 41);
            button17.TabIndex = 16;
            button17.Tag = "16";
            button17.Text = "  Face-2";
            button17.TextAlign = ContentAlignment.MiddleLeft;
            button17.UseVisualStyleBackColor = true;
            button17.Click += button1_Click;
            // 
            // button18
            // 
            button18.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button18.Image = (Image)resources.GetObject("button18.Image");
            button18.ImageAlign = ContentAlignment.MiddleRight;
            button18.Location = new Point(12, 153);
            button18.Name = "button18";
            button18.Size = new Size(103, 41);
            button18.TabIndex = 17;
            button18.Tag = "17";
            button18.Text = " FaceAcc";
            button18.TextAlign = ContentAlignment.MiddleLeft;
            button18.UseVisualStyleBackColor = true;
            button18.Click += button1_Click;
            // 
            // button19
            // 
            button19.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button19.Image = (Image)resources.GetObject("button19.Image");
            button19.ImageAlign = ContentAlignment.MiddleRight;
            button19.Location = new Point(230, 106);
            button19.Name = "button19";
            button19.Size = new Size(103, 41);
            button19.TabIndex = 18;
            button19.Tag = "18";
            button19.Text = " Glass";
            button19.TextAlign = ContentAlignment.MiddleLeft;
            button19.UseVisualStyleBackColor = true;
            button19.Click += button1_Click;
            // 
            // button20
            // 
            button20.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            button20.Image = (Image)resources.GetObject("button20.Image");
            button20.ImageAlign = ContentAlignment.MiddleRight;
            button20.Location = new Point(339, 106);
            button20.Name = "button20";
            button20.Size = new Size(103, 41);
            button20.TabIndex = 19;
            button20.Tag = "19";
            button20.Text = "Earring";
            button20.TextAlign = ContentAlignment.MiddleLeft;
            button20.UseVisualStyleBackColor = true;
            button20.Click += button1_Click;
            // 
            // SaveCharButton
            // 
            SaveCharButton.BackColor = SystemColors.GradientActiveCaption;
            SaveCharButton.Font = new Font("Microsoft JhengHei UI", 13F, FontStyle.Regular, GraphicsUnit.Pixel);
            SaveCharButton.Location = new Point(666, 153);
            SaveCharButton.Name = "SaveCharButton";
            SaveCharButton.Size = new Size(142, 62);
            SaveCharButton.TabIndex = 20;
            SaveCharButton.Text = "Save Character";
            SaveCharButton.UseVisualStyleBackColor = false;
            SaveCharButton.Click += SaveCharButton_Click;
            // 
            // tabControl1
            // 
            tabControl1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            tabControl1.Controls.Add(tabPage1);
            tabControl1.Controls.Add(tabPage2);
            tabControl1.Controls.Add(tabPage3);
            tabControl1.Controls.Add(tabPage7);
            tabControl1.Controls.Add(tabPage4);
            tabControl1.Controls.Add(tabPage5);
            tabControl1.Controls.Add(tabPage6);
            tabControl1.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            tabControl1.Location = new Point(12, 222);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(800, 700);
            tabControl1.TabIndex = 21;
            tabControl1.SelectedIndexChanged += tabControl1_SelectedIndexChanged;
            // 
            // tabPage1
            // 
            tabPage1.Location = new Point(4, 26);
            tabPage1.Name = "tabPage1";
            tabPage1.Padding = new Padding(3);
            tabPage1.Size = new Size(792, 670);
            tabPage1.TabIndex = 0;
            tabPage1.Text = "Equip";
            tabPage1.UseVisualStyleBackColor = true;
            tabPage1.MouseLeave += tabPage1_MouseLeave;
            // 
            // tabPage2
            // 
            tabPage2.Controls.Add(label9);
            tabPage2.Location = new Point(4, 26);
            tabPage2.Name = "tabPage2";
            tabPage2.Padding = new Padding(3);
            tabPage2.Size = new Size(792, 670);
            tabPage2.TabIndex = 1;
            tabPage2.Text = "Load  Character";
            tabPage2.UseVisualStyleBackColor = true;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Font = new Font("Tahoma", 48F, FontStyle.Regular, GraphicsUnit.Pixel);
            label9.Location = new Point(277, 278);
            label9.Name = "label9";
            label9.Size = new Size(237, 58);
            label9.TabIndex = 0;
            label9.Text = "Loading...";
            label9.Visible = false;
            // 
            // tabPage3
            // 
            tabPage3.Controls.Add(DyeGrid);
            tabPage3.Location = new Point(4, 26);
            tabPage3.Name = "tabPage3";
            tabPage3.Padding = new Padding(3);
            tabPage3.Size = new Size(792, 670);
            tabPage3.TabIndex = 2;
            tabPage3.Text = "Dye";
            tabPage3.UseVisualStyleBackColor = true;
            // 
            // DyeGrid
            // 
            DyeGrid.AllowUserToAddRows = false;
            DyeGrid.AllowUserToResizeColumns = false;
            DyeGrid.AllowUserToResizeRows = false;
            DyeGrid.BackgroundColor = SystemColors.ButtonFace;
            DyeGrid.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            DyeGrid.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            DyeGrid.ColumnHeadersVisible = false;
            DyeGrid.Dock = DockStyle.Fill;
            DyeGrid.Location = new Point(3, 3);
            DyeGrid.MultiSelect = false;
            DyeGrid.Name = "DyeGrid";
            DyeGrid.RowHeadersVisible = false;
            DyeGrid.RowHeadersWidth = 40;
            DyeGrid.RowTemplate.Height = 40;
            DyeGrid.ShowCellToolTips = false;
            DyeGrid.Size = new Size(786, 664);
            DyeGrid.TabIndex = 0;
            DyeGrid.CellClick += DyeGrid_CellClick;
            // 
            // tabPage7
            // 
            tabPage7.Controls.Add(button22);
            tabPage7.Controls.Add(LabelLightness);
            tabPage7.Controls.Add(LabelSat);
            tabPage7.Controls.Add(LabelHue);
            tabPage7.Controls.Add(label8);
            tabPage7.Controls.Add(label7);
            tabPage7.Controls.Add(label6);
            tabPage7.Controls.Add(LightnessTrackBar);
            tabPage7.Controls.Add(SatTrackBar);
            tabPage7.Controls.Add(HueTrackBar);
            tabPage7.Controls.Add(DyePicture);
            tabPage7.Controls.Add(DyeGrid2);
            tabPage7.Location = new Point(4, 26);
            tabPage7.Name = "tabPage7";
            tabPage7.Size = new Size(792, 670);
            tabPage7.TabIndex = 6;
            tabPage7.Text = "Dye2";
            tabPage7.UseVisualStyleBackColor = true;
            // 
            // button22
            // 
            button22.Location = new Point(295, 526);
            button22.Name = "button22";
            button22.Size = new Size(144, 44);
            button22.TabIndex = 11;
            button22.Text = "OK";
            button22.UseVisualStyleBackColor = true;
            button22.Click += button22_Click;
            // 
            // LabelLightness
            // 
            LabelLightness.AutoSize = true;
            LabelLightness.Font = new Font("Tahoma", 16F, FontStyle.Regular, GraphicsUnit.Pixel);
            LabelLightness.Location = new Point(152, 477);
            LabelLightness.Name = "LabelLightness";
            LabelLightness.Size = new Size(18, 19);
            LabelLightness.TabIndex = 10;
            LabelLightness.Text = "0";
            // 
            // LabelSat
            // 
            LabelSat.AutoSize = true;
            LabelSat.Font = new Font("Tahoma", 16F, FontStyle.Regular, GraphicsUnit.Pixel);
            LabelSat.Location = new Point(152, 435);
            LabelSat.Name = "LabelSat";
            LabelSat.Size = new Size(18, 19);
            LabelSat.TabIndex = 9;
            LabelSat.Text = "0";
            // 
            // LabelHue
            // 
            LabelHue.AutoSize = true;
            LabelHue.Font = new Font("Tahoma", 16F, FontStyle.Regular, GraphicsUnit.Pixel);
            LabelHue.Location = new Point(152, 393);
            LabelHue.Name = "LabelHue";
            LabelHue.Size = new Size(18, 19);
            LabelHue.TabIndex = 8;
            LabelHue.Text = "0";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Font = new Font("Tahoma", 16F, FontStyle.Regular, GraphicsUnit.Pixel);
            label8.Location = new Point(68, 477);
            label8.Name = "label8";
            label8.Size = new Size(86, 19);
            label8.TabIndex = 7;
            label8.Text = "Lightness :";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Font = new Font("Tahoma", 16F, FontStyle.Regular, GraphicsUnit.Pixel);
            label7.Location = new Point(62, 435);
            label7.Name = "label7";
            label7.Size = new Size(92, 19);
            label7.TabIndex = 6;
            label7.Text = "Saturation :";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new Font("Tahoma", 16F, FontStyle.Regular, GraphicsUnit.Pixel);
            label6.Location = new Point(106, 393);
            label6.Name = "label6";
            label6.Size = new Size(48, 19);
            label6.TabIndex = 5;
            label6.Text = "Hue :";
            // 
            // LightnessTrackBar
            // 
            LightnessTrackBar.AutoSize = false;
            LightnessTrackBar.Location = new Point(202, 475);
            LightnessTrackBar.Maximum = 100;
            LightnessTrackBar.Minimum = -100;
            LightnessTrackBar.Name = "LightnessTrackBar";
            LightnessTrackBar.Size = new Size(356, 25);
            LightnessTrackBar.TabIndex = 4;
            LightnessTrackBar.TickStyle = TickStyle.None;
            LightnessTrackBar.Scroll += LightnessTrackBar_Scroll;
            // 
            // SatTrackBar
            // 
            SatTrackBar.AutoSize = false;
            SatTrackBar.LargeChange = 1;
            SatTrackBar.Location = new Point(202, 431);
            SatTrackBar.Maximum = 100;
            SatTrackBar.Minimum = -100;
            SatTrackBar.Name = "SatTrackBar";
            SatTrackBar.Size = new Size(356, 25);
            SatTrackBar.TabIndex = 3;
            SatTrackBar.TickStyle = TickStyle.None;
            SatTrackBar.Scroll += SatTrackBar_Scroll;
            // 
            // HueTrackBar
            // 
            HueTrackBar.AutoSize = false;
            HueTrackBar.LargeChange = 1;
            HueTrackBar.Location = new Point(202, 389);
            HueTrackBar.Maximum = 360;
            HueTrackBar.Name = "HueTrackBar";
            HueTrackBar.Size = new Size(356, 25);
            HueTrackBar.TabIndex = 2;
            HueTrackBar.TickStyle = TickStyle.None;
            HueTrackBar.Scroll += HueTrackBar_Scroll;
            // 
            // DyePicture
            // 
            DyePicture.BorderStyle = BorderStyle.FixedSingle;
            DyePicture.Location = new Point(325, 300);
            DyePicture.Name = "DyePicture";
            DyePicture.Size = new Size(93, 79);
            DyePicture.SizeMode = PictureBoxSizeMode.StretchImage;
            DyePicture.TabIndex = 1;
            DyePicture.TabStop = false;
            // 
            // DyeGrid2
            // 
            DyeGrid2.AllowUserToAddRows = false;
            DyeGrid2.AllowUserToDeleteRows = false;
            DyeGrid2.AllowUserToResizeColumns = false;
            DyeGrid2.AllowUserToResizeRows = false;
            DyeGrid2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            DyeGrid2.ColumnHeadersVisible = false;
            DyeGrid2.Location = new Point(202, 24);
            DyeGrid2.Name = "DyeGrid2";
            DyeGrid2.RowHeadersVisible = false;
            DyeGrid2.RowHeadersWidth = 51;
            DyeGrid2.RowTemplate.Height = 29;
            DyeGrid2.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            DyeGrid2.Size = new Size(356, 268);
            DyeGrid2.TabIndex = 0;
            DyeGrid2.CellClick += DyeGrid2_CellClick;
            // 
            // tabPage4
            // 
            tabPage4.Controls.Add(textBox1);
            tabPage4.Controls.Add(label3);
            tabPage4.Controls.Add(panel1);
            tabPage4.Location = new Point(4, 26);
            tabPage4.Name = "tabPage4";
            tabPage4.Padding = new Padding(3);
            tabPage4.Size = new Size(792, 670);
            tabPage4.TabIndex = 3;
            tabPage4.Text = "Search";
            tabPage4.UseVisualStyleBackColor = true;
            // 
            // textBox1
            // 
            textBox1.Location = new Point(184, 76);
            textBox1.Name = "textBox1";
            textBox1.Size = new Size(245, 24);
            textBox1.TabIndex = 2;
            textBox1.TextChanged += textBox1_TextChanged;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(116, 79);
            label3.Name = "label3";
            label3.Size = new Size(50, 17);
            label3.TabIndex = 1;
            label3.Text = "Search";
            // 
            // panel1
            // 
            panel1.BorderStyle = BorderStyle.FixedSingle;
            panel1.Controls.Add(UseButton);
            panel1.Controls.Add(pictureBox1);
            panel1.Controls.Add(label1);
            panel1.Controls.Add(label2);
            panel1.Location = new Point(114, 21);
            panel1.Name = "panel1";
            panel1.Size = new Size(315, 49);
            panel1.TabIndex = 0;
            // 
            // UseButton
            // 
            UseButton.Location = new Point(263, 6);
            UseButton.Name = "UseButton";
            UseButton.Size = new Size(45, 35);
            UseButton.TabIndex = 3;
            UseButton.Text = "Use";
            UseButton.UseVisualStyleBackColor = true;
            UseButton.Click += UseButton_Click;
            // 
            // pictureBox1
            // 
            pictureBox1.Location = new Point(74, 3);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(40, 40);
            pictureBox1.SizeMode = PictureBoxSizeMode.CenterImage;
            pictureBox1.TabIndex = 1;
            pictureBox1.TabStop = false;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(3, 16);
            label1.Name = "label1";
            label1.Size = new Size(0, 17);
            label1.TabIndex = 0;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(110, 16);
            label2.Name = "label2";
            label2.Size = new Size(0, 17);
            label2.TabIndex = 2;
            // 
            // tabPage5
            // 
            tabPage5.Location = new Point(4, 26);
            tabPage5.Name = "tabPage5";
            tabPage5.Padding = new Padding(3);
            tabPage5.Size = new Size(792, 670);
            tabPage5.TabIndex = 4;
            tabPage5.Text = "Spawn";
            tabPage5.UseVisualStyleBackColor = true;
            // 
            // tabPage6
            // 
            tabPage6.Controls.Add(checkBox1);
            tabPage6.Controls.Add(saveSprite_button);
            tabPage6.Controls.Add(saveSpriteSheet_button);
            tabPage6.Controls.Add(saveAllSprite_button);
            tabPage6.Controls.Add(customAABB_checkBox);
            tabPage6.Controls.Add(groupBox1);
            tabPage6.Controls.Add(panel2);
            tabPage6.Controls.Add(FrameListBox);
            tabPage6.Location = new Point(4, 26);
            tabPage6.Name = "tabPage6";
            tabPage6.Padding = new Padding(3);
            tabPage6.Size = new Size(792, 670);
            tabPage6.TabIndex = 5;
            tabPage6.Text = "Export";
            tabPage6.UseVisualStyleBackColor = true;
            // 
            // checkBox1
            // 
            checkBox1.AutoSize = true;
            checkBox1.Location = new Point(634, 16);
            checkBox1.Name = "checkBox1";
            checkBox1.Size = new Size(107, 21);
            checkBox1.TabIndex = 28;
            checkBox1.Text = "debug draw";
            checkBox1.UseVisualStyleBackColor = true;
            checkBox1.CheckedChanged += checkBox1_CheckedChanged;
            // 
            // saveSprite_button
            // 
            saveSprite_button.Location = new Point(155, 10);
            saveSprite_button.Name = "saveSprite_button";
            saveSprite_button.Size = new Size(150, 31);
            saveSprite_button.TabIndex = 2;
            saveSprite_button.Text = "Export Current Sprite";
            saveSprite_button.UseVisualStyleBackColor = true;
            saveSprite_button.Click += ExportSprite;
            // 
            // saveSpriteSheet_button
            // 
            saveSpriteSheet_button.Location = new Point(467, 10);
            saveSpriteSheet_button.Name = "saveSpriteSheet_button";
            saveSpriteSheet_button.Size = new Size(150, 31);
            saveSpriteSheet_button.TabIndex = 17;
            saveSpriteSheet_button.Text = "Export SpriteSheet";
            saveSpriteSheet_button.UseVisualStyleBackColor = true;
            saveSpriteSheet_button.Click += ExportSpriteSheet;
            // 
            // saveAllSprite_button
            // 
            saveAllSprite_button.Location = new Point(311, 10);
            saveAllSprite_button.Name = "saveAllSprite_button";
            saveAllSprite_button.Size = new Size(150, 31);
            saveAllSprite_button.TabIndex = 16;
            saveAllSprite_button.Text = "Export All Sprites";
            saveAllSprite_button.UseVisualStyleBackColor = true;
            saveAllSprite_button.Click += ExportAllSprite;
            // 
            // customAABB_checkBox
            // 
            customAABB_checkBox.AutoSize = true;
            customAABB_checkBox.Location = new Point(165, 50);
            customAABB_checkBox.Name = "customAABB_checkBox";
            customAABB_checkBox.Size = new Size(172, 21);
            customAABB_checkBox.TabIndex = 27;
            customAABB_checkBox.Text = "Custom bounding box";
            customAABB_checkBox.UseVisualStyleBackColor = true;
            customAABB_checkBox.CheckedChanged += customAABB_checkBox_CheckedChanged;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(ScrollBarH);
            groupBox1.Controls.Add(label10);
            groupBox1.Controls.Add(AdjH);
            groupBox1.Controls.Add(Xlabel);
            groupBox1.Controls.Add(AdjX);
            groupBox1.Controls.Add(ScrollBarX);
            groupBox1.Controls.Add(ScrollBarW);
            groupBox1.Controls.Add(AdjW);
            groupBox1.Controls.Add(YLabel);
            groupBox1.Controls.Add(AdjY);
            groupBox1.Controls.Add(label13);
            groupBox1.Controls.Add(ScrollBarY);
            groupBox1.Enabled = false;
            groupBox1.Location = new Point(155, 47);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(512, 91);
            groupBox1.TabIndex = 15;
            groupBox1.TabStop = false;
            // 
            // ScrollBarH
            // 
            ScrollBarH.LargeChange = 1;
            ScrollBarH.Location = new Point(290, 60);
            ScrollBarH.Maximum = 512;
            ScrollBarH.Minimum = 32;
            ScrollBarH.Name = "ScrollBarH";
            ScrollBarH.Size = new Size(160, 17);
            ScrollBarH.TabIndex = 13;
            ScrollBarH.Value = 256;
            ScrollBarH.Scroll += hScrollBar1_Scroll;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new Point(240, 60);
            label10.Name = "label10";
            label10.Size = new Size(22, 17);
            label10.TabIndex = 14;
            label10.Text = "H:";
            // 
            // AdjH
            // 
            AdjH.AutoSize = true;
            AdjH.Location = new Point(260, 60);
            AdjH.Name = "AdjH";
            AdjH.Size = new Size(32, 17);
            AdjH.TabIndex = 12;
            AdjH.Text = "256";
            // 
            // Xlabel
            // 
            Xlabel.AutoSize = true;
            Xlabel.Location = new Point(10, 30);
            Xlabel.Name = "Xlabel";
            Xlabel.Size = new Size(21, 17);
            Xlabel.TabIndex = 3;
            Xlabel.Text = "X:";
            // 
            // AdjX
            // 
            AdjX.AutoSize = true;
            AdjX.Location = new Point(30, 30);
            AdjX.Name = "AdjX";
            AdjX.Size = new Size(32, 17);
            AdjX.TabIndex = 5;
            AdjX.Text = "128";
            // 
            // ScrollBarX
            // 
            ScrollBarX.LargeChange = 1;
            ScrollBarX.Location = new Point(60, 30);
            ScrollBarX.Maximum = 512;
            ScrollBarX.Name = "ScrollBarX";
            ScrollBarX.Size = new Size(160, 18);
            ScrollBarX.TabIndex = 4;
            ScrollBarX.Value = 128;
            ScrollBarX.Scroll += hScrollBar1_Scroll;
            // 
            // ScrollBarW
            // 
            ScrollBarW.LargeChange = 1;
            ScrollBarW.Location = new Point(60, 60);
            ScrollBarW.Maximum = 512;
            ScrollBarW.Minimum = 32;
            ScrollBarW.Name = "ScrollBarW";
            ScrollBarW.ScaleScrollBarForDpiChange = false;
            ScrollBarW.Size = new Size(160, 18);
            ScrollBarW.TabIndex = 10;
            ScrollBarW.Value = 256;
            ScrollBarW.Scroll += hScrollBar1_Scroll;
            // 
            // AdjW
            // 
            AdjW.AutoSize = true;
            AdjW.Location = new Point(30, 60);
            AdjW.Name = "AdjW";
            AdjW.Size = new Size(32, 17);
            AdjW.TabIndex = 11;
            AdjW.Text = "256";
            // 
            // YLabel
            // 
            YLabel.AutoSize = true;
            YLabel.Location = new Point(240, 30);
            YLabel.Name = "YLabel";
            YLabel.Size = new Size(21, 17);
            YLabel.TabIndex = 8;
            YLabel.Text = "Y:";
            // 
            // AdjY
            // 
            AdjY.AutoSize = true;
            AdjY.Location = new Point(260, 30);
            AdjY.Name = "AdjY";
            AdjY.Size = new Size(32, 17);
            AdjY.TabIndex = 6;
            AdjY.Text = "128";
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.Location = new Point(10, 60);
            label13.Name = "label13";
            label13.Size = new Size(27, 17);
            label13.TabIndex = 9;
            label13.Text = "W:";
            // 
            // ScrollBarY
            // 
            ScrollBarY.LargeChange = 1;
            ScrollBarY.Location = new Point(290, 30);
            ScrollBarY.Maximum = 512;
            ScrollBarY.Name = "ScrollBarY";
            ScrollBarY.Size = new Size(160, 17);
            ScrollBarY.TabIndex = 7;
            ScrollBarY.Value = 128;
            ScrollBarY.Scroll += hScrollBar1_Scroll;
            // 
            // panel2
            // 
            panel2.Location = new Point(155, 144);
            panel2.Name = "panel2";
            panel2.Size = new Size(512, 512);
            panel2.TabIndex = 1;
            // 
            // FrameListBox
            // 
            FrameListBox.FormattingEnabled = true;
            FrameListBox.ItemHeight = 17;
            FrameListBox.Location = new Point(6, 6);
            FrameListBox.Name = "FrameListBox";
            FrameListBox.Size = new Size(143, 650);
            FrameListBox.TabIndex = 0;
            FrameListBox.SelectedIndexChanged += FrameListBox_SelectedIndexChanged;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            label4.Location = new Point(446, 162);
            label4.Name = "label4";
            label4.Size = new Size(74, 17);
            label4.TabIndex = 22;
            label4.Text = "Expression";
            // 
            // comboBox1
            // 
            comboBox1.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox1.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            comboBox1.FormattingEnabled = true;
            comboBox1.Items.AddRange(new object[] { "blink", "hit", "smile", "troubled", "cry", "angry", "bewildered", "stunned", "vomit", "oops", "cheers", "chu", "wink", "pain", "glitter", "despair", "love", "shine", "blaze", "hum", "bowing", "hot", "dam", "qBlue" });
            comboBox1.Location = new Point(526, 159);
            comboBox1.Name = "comboBox1";
            comboBox1.Size = new Size(134, 25);
            comboBox1.TabIndex = 23;
            comboBox1.SelectedIndexChanged += comboBox1_SelectedIndexChanged;
            // 
            // ShowToolTil_CheckBox
            // 
            ShowToolTil_CheckBox.AutoSize = true;
            ShowToolTil_CheckBox.Checked = true;
            ShowToolTil_CheckBox.CheckState = CheckState.Checked;
            ShowToolTil_CheckBox.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            ShowToolTil_CheckBox.Location = new Point(358, 161);
            ShowToolTil_CheckBox.Name = "ShowToolTil_CheckBox";
            ShowToolTil_CheckBox.Size = new Size(82, 21);
            ShowToolTil_CheckBox.TabIndex = 24;
            ShowToolTil_CheckBox.Text = "Tool Tip";
            ShowToolTil_CheckBox.UseVisualStyleBackColor = true;
            ShowToolTil_CheckBox.CheckedChanged += ShowToolTil_CheckBox_CheckedChanged;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            label5.Location = new Point(492, 193);
            label5.Name = "label5";
            label5.Size = new Size(28, 17);
            label5.TabIndex = 25;
            label5.Text = "Ear";
            // 
            // EarListBox
            // 
            EarListBox.Font = new Font("Tahoma", 14F, FontStyle.Regular, GraphicsUnit.Pixel);
            EarListBox.FormattingEnabled = true;
            EarListBox.Location = new Point(526, 190);
            EarListBox.Name = "EarListBox";
            EarListBox.Size = new Size(134, 25);
            EarListBox.TabIndex = 26;
            EarListBox.SelectedIndexChanged += EarListBox_SelectedIndexChanged;
            // 
            // timer1
            // 
            timer1.Interval = 10;
            timer1.Tick += timer1_Tick;
            // 
            // AvatarForm
            // 
            AutoScaleMode = AutoScaleMode.None;
            ClientSize = new Size(1130, 929);
            Controls.Add(EarListBox);
            Controls.Add(label5);
            Controls.Add(ShowToolTil_CheckBox);
            Controls.Add(comboBox1);
            Controls.Add(label4);
            Controls.Add(tabControl1);
            Controls.Add(SaveCharButton);
            Controls.Add(button20);
            Controls.Add(button19);
            Controls.Add(button18);
            Controls.Add(button17);
            Controls.Add(button16);
            Controls.Add(button15);
            Controls.Add(button14);
            Controls.Add(button13);
            Controls.Add(button12);
            Controls.Add(button11);
            Controls.Add(button10);
            Controls.Add(button9);
            Controls.Add(button8);
            Controls.Add(button7);
            Controls.Add(button6);
            Controls.Add(button5);
            Controls.Add(button4);
            Controls.Add(button3);
            Controls.Add(button2);
            Controls.Add(button1);
            Font = new Font("Microsoft JhengHei UI", 9F, FontStyle.Regular, GraphicsUnit.Pixel);
            MinimumSize = new Size(1000, 0);
            Name = "AvatarForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Avatar";
            TopMost = true;
            FormClosing += AvatarForm_FormClosing;
            Load += AvatarForm_Load;
            tabControl1.ResumeLayout(false);
            tabPage2.ResumeLayout(false);
            tabPage2.PerformLayout();
            tabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)DyeGrid).EndInit();
            tabPage7.ResumeLayout(false);
            tabPage7.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)LightnessTrackBar).EndInit();
            ((System.ComponentModel.ISupportInitialize)SatTrackBar).EndInit();
            ((System.ComponentModel.ISupportInitialize)HueTrackBar).EndInit();
            ((System.ComponentModel.ISupportInitialize)DyePicture).EndInit();
            ((System.ComponentModel.ISupportInitialize)DyeGrid2).EndInit();
            tabPage4.ResumeLayout(false);
            tabPage4.PerformLayout();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            tabPage6.ResumeLayout(false);
            tabPage6.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Button button1;
        private Button button2;
        private Button button3;
        private Button button4;
        private Button button5;
        private Button button6;
        private Button button7;
        private Button button8;
        private Button button9;
        private Button button10;
        private Button button11;
        private Button button12;
        private Button button13;
        private Button button14;
        private Button button15;
        private Button button16;
        private Button button17;
        private Button button18;
        private Button button19;
        private Button button20;
        private Button SaveCharButton;
        private TabControl tabControl1;
        private TabPage tabPage1;
        private TabPage tabPage2;
        private TabPage tabPage3;
        private DataGridView DyeGrid;
        private TabPage tabPage4;
        private Panel panel1;
        private Label label2;
        private PictureBox pictureBox1;
        private Label label1;
        private TextBox textBox1;
        private Label label3;
        private Button UseButton;
        private TabPage tabPage5;
        private Label label4;
        public ComboBox comboBox1;
        private CheckBox ShowToolTil_CheckBox;
        private Label label5;
        private ComboBox EarListBox;
        private TabPage tabPage6;
        private ListBox FrameListBox;
        private Panel panel2;
        private Label Xlabel;
        private Button saveSprite_button;
        private Label label10;
        private HScrollBar ScrollBarH;
        private Label AdjH;
        private Label AdjW;
        private HScrollBar ScrollBarW;
        private Label label13;
        private Label YLabel;
        private HScrollBar ScrollBarY;
        private Label AdjY;
        private Label AdjX;
        private HScrollBar ScrollBarX;
        private TabPage tabPage7;
        private DataGridView DyeGrid2;
        private PictureBox DyePicture;
        private TrackBar SatTrackBar;
        private TrackBar HueTrackBar;
        private TrackBar LightnessTrackBar;
        private Label LabelLightness;
        private Label LabelSat;
        private Label LabelHue;
        private Label label8;
        private Label label7;
        private Label label6;
        private Button button22;
        private GroupBox groupBox1;
        private Button saveSpriteSheet_button;
        private Button saveAllSprite_button;
        private CheckBox customAABB_checkBox;
        private System.Windows.Forms.Timer timer1;
        private Label label9;
        private CheckBox checkBox1;
    }
}